'use client';

import React, { useState } from 'react';
import { useRouter } from '@/i18n/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { useCreateMenuItemMutation } from '@/lib/redux/api/endpoints/menuApi';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { Link } from '@/i18n/navigation';

// Form schema
const addMenuItemSchema = z.object({
  name: z.string().min(1, 'Item name is required'),
  description: z.string().optional(),
  price: z.preprocess(
    (val) => (val === '' ? 0 : Number(val)),
    z.number().min(0, 'Price must be a positive number')
  ),
});

type AddMenuItemFormValues = z.infer<typeof addMenuItemSchema>;

interface AddMenuItemPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function AddMenuItemPage({ params }: AddMenuItemPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const router = useRouter();
  const [createMenuItem, { isLoading }] = useCreateMenuItemMutation();

  // Get merchant and branch data from backend
  const { data: merchantsData, isLoading: isLoadingMerchants } = useGetMerchantsQuery({});

  // Find the merchant by slug
  const merchant = merchantsData?.data?.find(m => m.slug === slugShop);
  const branch = merchant?.branches?.find(b => b.slug === slugBranch);

  // State for image upload
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // Form setup
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<AddMenuItemFormValues>({
    resolver: zodResolver(addMenuItemSchema),
    defaultValues: {
      name: '',
      description: '',
      price: 0,
    },
  });

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle form submission
  const onSubmit = async (data: AddMenuItemFormValues) => {
    if (!merchant || !branch) {
      toast.error('Restaurant or branch not found');
      return;
    }

    try {
      // In a real app, you would upload the image to a storage service
      // and get back a URL to store in the database
      const imageUrl = imagePreview || '';

      await createMenuItem({
        shopId: merchant.id,
        branchId: branch.id,
        name: data.name,
        description: data.description || '',
        price: data.price,
        images: imageUrl ? [imageUrl] : [],
        is_available: true,
      }).unwrap();

      toast.success('Menu item added successfully');
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/menu`);
    } catch (error) {
      console.error('Failed to add menu item:', error);
      toast.error('Failed to add menu item');
    }
  };

  // Show loading state
  if (isLoadingMerchants) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#161412] mx-auto mb-4"></div>
            <p className="text-[#81766a]">Loading restaurant information...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if merchant or branch not found
  if (!merchant || !branch) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6 p-4">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Menu
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-[#81766a] text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="font-be-vietnam">
      <div className="flex items-center mb-6 p-4">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Menu
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 p-4">
        <p className="text-[#161412] tracking-light text-[32px] font-bold leading-tight min-w-72">Add New Menu Item</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col max-w-[512px] mx-auto">
        <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
          <label className="flex flex-col min-w-40 flex-1">
            <p className="text-[#161412] text-base font-medium leading-normal pb-2">Item Name</p>
            <input
              {...register('name')}
              placeholder="Enter item name"
              className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#161412] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f1] focus:border-none h-14 placeholder:text-[#81766a] p-4 text-base font-normal leading-normal"
            />
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>}
          </label>
        </div>

        <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
          <label className="flex flex-col min-w-40 flex-1">
            <p className="text-[#161412] text-base font-medium leading-normal pb-2">Description</p>
            <textarea
              {...register('description')}
              placeholder="Enter item description"
              className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#161412] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f1] focus:border-none min-h-36 placeholder:text-[#81766a] p-4 text-base font-normal leading-normal"
            />
            {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>}
          </label>
        </div>

        <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
          <label className="flex flex-col min-w-40 flex-1">
            <p className="text-[#161412] text-base font-medium leading-normal pb-2">Price</p>
            <input
              {...register('price')}
              placeholder="Enter item price"
              type="number"
              step="0.01"
              min="0"
              className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#161412] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f1] focus:border-none h-14 placeholder:text-[#81766a] p-4 text-base font-normal leading-normal"
            />
            {errors.price && <p className="text-red-500 text-sm mt-1">{errors.price.message}</p>}
          </label>
        </div>



        <div className="flex flex-col p-4">
          <div className="flex flex-col items-center gap-6 rounded-xl border-2 border-dashed border-[#e3e1dd] px-6 py-14">
            {imagePreview ? (
              <div className="flex flex-col items-center gap-4">
                <img src={imagePreview} alt="Preview" className="max-w-[200px] max-h-[200px] rounded-lg" />
                <button
                  type="button"
                  onClick={() => {
                    setImagePreview(null);
                  }}
                  className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#f4f2f1] text-[#161412] text-sm font-bold leading-normal tracking-[0.015em]"
                >
                  <span className="truncate">Remove Image</span>
                </button>
              </div>
            ) : (
              <>
                <div className="flex max-w-[480px] flex-col items-center gap-2">
                  <p className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] max-w-[480px] text-center">Upload Image</p>
                  <p className="text-[#161412] text-sm font-normal leading-normal max-w-[480px] text-center">Click or drag an image here to upload</p>
                </div>
                <input
                  type="file"
                  id="image-upload"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
                <label
                  htmlFor="image-upload"
                  className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#f4f2f1] text-[#161412] text-sm font-bold leading-normal tracking-[0.015em]"
                >
                  <span className="truncate">Upload Image</span>
                </label>
              </>
            )}
          </div>
        </div>

        <div className="flex px-4 py-3 justify-end">
          <button
            type="submit"
            disabled={isLoading}
            className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e5ccb2] text-[#161412] text-sm font-bold leading-normal tracking-[0.015em] disabled:opacity-50"
          >
            <span className="truncate">{isLoading ? 'Adding...' : 'Add Item'}</span>
          </button>
        </div>
      </form>
    </div>
  );
}
